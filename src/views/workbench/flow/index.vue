<template>
  <div class="flow-editor">
    <!-- 顶部工具栏 -->
    <div class="flow-toolbar">
      <n-space>
        <n-button type="primary" @click="saveFlow">
          <template #icon>
            <n-icon><SaveOutlined /></n-icon>
          </template>
          保存流程
        </n-button>
        <n-button @click="previewFlow">
          <template #icon>
            <n-icon><EyeOutlined /></n-icon>
          </template>
          预览
        </n-button>
        <n-button @click="clearFlow">
          <template #icon>
            <n-icon><ClearOutlined /></n-icon>
          </template>
          清空
        </n-button>
        <n-divider vertical />
        <n-button @click="zoomIn">
          <template #icon>
            <n-icon><ZoomInOutlined /></n-icon>
          </template>
        </n-button>
        <n-button @click="zoomOut">
          <template #icon>
            <n-icon><ZoomOutOutlined /></n-icon>
          </template>
        </n-button>
        <n-button @click="zoomToFit">适应画布</n-button>
      </n-space>
    </div>

    <div class="flow-content">
      <!-- 左侧工具面板 -->
      <div class="flow-sidebar">
        <div class="sidebar-section">
          <h4>开始节点</h4>
          <div class="node-item" @mousedown="startDrag('start')">
            <div class="node-icon start-node">
              <n-icon><PlayCircleOutlined /></n-icon>
            </div>
            <span>数据输入</span>
          </div>
          <div class="node-item" @mousedown="startDrag('import')">
            <div class="node-icon start-node">
              <n-icon><ImportOutlined /></n-icon>
            </div>
            <span>导入数据</span>
          </div>
        </div>

        <div class="sidebar-section">
          <h4>转换节点</h4>
          <div class="node-item" @mousedown="startDrag('transform')">
            <div class="node-icon transform-node">
              <n-icon><SwapOutlined /></n-icon>
            </div>
            <span>数据转换</span>
          </div>
          <div class="node-item" @mousedown="startDrag('filter')">
            <div class="node-icon transform-node">
              <n-icon><FilterOutlined /></n-icon>
            </div>
            <span>数据清洗</span>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="flow-canvas">
        <div ref="containerRef" class="x6-container"></div>
      </div>

      <!-- 右侧属性面板 -->
      <div v-show="selectedNode" class="flow-properties">
        <div class="properties-header">
          <h4>节点属性</h4>
        </div>
        <div class="properties-content">
          <n-form v-if="selectedNode" :model="nodeForm" label-placement="top">
            <n-form-item label="节点名称">
              <n-input v-model:value="nodeForm.name" @blur="updateNodeName" />
            </n-form-item>
            <n-form-item label="类型">
              <n-input :value="nodeForm.type" readonly />
            </n-form-item>
            <n-form-item label="top坐标">
              <n-input-number v-model:value="nodeForm.top" @blur="updateNodePosition" />
            </n-form-item>
            <n-form-item label="left坐标">
              <n-input-number v-model:value="nodeForm.left" @blur="updateNodePosition" />
            </n-form-item>

            <!-- 条件节点特有属性 -->
            <template v-if="nodeForm.type === 'condition'">
              <n-form-item label="条件表达式">
                <n-input v-model:value="nodeForm.condition" type="textarea" />
              </n-form-item>
            </template>

            <!-- 数据转换节点特有属性 -->
            <template v-if="nodeForm.type === 'transform'">
              <n-form-item label="转换规则">
                <n-input v-model:value="nodeForm.transformRule" type="textarea" />
              </n-form-item>
            </template>
          </n-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { Graph, Shape } from '@antv/x6'
import { useMessage, useDialog } from 'naive-ui'
import {
  SaveOutlined,
  EyeOutlined,
  ClearOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  PlayCircleOutlined,
  ImportOutlined,
  SwapOutlined,
  FilterOutlined,
} from '@ant-design/icons-vue'

// 使用 Naive UI 组件
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const containerRef = ref()
const selectedNode = ref(null)
const graph = ref(null)
const nodeForm = ref({
  name: '',
  type: '',
  top: 0,
  left: 0,
  condition: '',
  transformRule: '',
})

// 节点类型配置
const nodeTypes = {
  start: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#5F95FF',
        stroke: '#5F95FF',
        rx: 20,
        ry: 20,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
  import: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#47C769',
        stroke: '#47C769',
        rx: 20,
        ry: 20,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
  transform: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#FF9C6E',
        stroke: '#FF9C6E',
        rx: 6,
        ry: 6,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
  filter: {
    shape: 'rect',
    width: 120,
    height: 40,
    attrs: {
      body: {
        fill: '#FF6B9D',
        stroke: '#FF6B9D',
        rx: 6,
        ry: 6,
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
  condition: {
    shape: 'polygon',
    width: 120,
    height: 60,
    attrs: {
      body: {
        fill: '#FFC069',
        stroke: '#FFC069',
        refPoints: '0,10 10,0 20,10 10,20',
      },
      text: {
        fill: 'white',
        fontSize: 12,
      },
    },
  },
}

// 初始化图形编辑器
const initGraph = () => {
  graph.value = new Graph({
    container: containerRef.value,
    width: containerRef.value.offsetWidth,
    height: containerRef.value.offsetHeight,
    grid: {
      visible: true,
      type: 'doubleMesh',
      args: [
        {
          color: '#eee',
          thickness: 1,
        },
        {
          color: '#ddd',
          thickness: 1,
          factor: 4,
        },
      ],
    },
    selecting: {
      enabled: true,
      rubberband: true,
      showNodeSelectionBox: true,
    },
    connecting: {
      router: 'manhattan',
      connector: {
        name: 'rounded',
        args: {
          radius: 8,
        },
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false,
      snap: {
        radius: 20,
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8,
              },
            },
          },
          zIndex: 0,
        })
      },
      validateConnection({ targetMagnet }) {
        return !!targetMagnet
      },
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF',
          },
        },
      },
    },
    resizing: true,
    rotating: true,
    history: true,
    clipboard: true,
    keyboard: true,
  })

  // 绑定事件
  bindEvents()
}

// 绑定图形事件
const bindEvents = () => {
  // 节点选中事件
  graph.value.on('node:click', ({ node }) => {
    selectedNode.value = node
    const data = node.getData() || {}
    const position = node.position()

    nodeForm.value = {
      name: node.label || data.name || '',
      type: data.type || '',
      top: position.y,
      left: position.x,
      condition: data.condition || '',
      transformRule: data.transformRule || '',
    }
  })

  // 画布点击事件（取消选中）
  graph.value.on('blank:click', () => {
    selectedNode.value = null
    nodeForm.value = {
      name: '',
      type: '',
      top: 0,
      left: 0,
      condition: '',
      transformRule: '',
    }
  })

  // 连线创建事件
  graph.value.on('edge:connected', ({ edge }) => {
    const sourceNode = edge.getSourceNode()

    // 如果源节点是条件节点，需要设置连线标签
    if (sourceNode && sourceNode.getData()?.type === 'condition') {
      showConditionDialog(edge)
    }
  })
}

// 显示条件设置对话框
const showConditionDialog = (edge) => {
  // 这里可以弹出对话框让用户设置条件标签
  const condition = prompt('请输入条件标签（如：是/否、通过/不通过）：')
  if (condition) {
    edge.setLabels([
      {
        attrs: {
          text: {
            text: condition,
            fill: '#333',
            fontSize: 12,
          },
        },
      },
    ])
  }
}

// 获取节点标签
const getNodeLabel = (type) => {
  const labels = {
    start: '数据输入',
    import: '导入数据',
    transform: '数据转换',
    filter: '数据清洗',
    condition: '条件判断',
  }
  return labels[type] || type
}

// 工具栏方法
const saveFlow = () => {
  const data = graph.value.toJSON()
  console.log('保存流程数据:', data)
  message.success('流程保存成功')
}

const previewFlow = () => {
  const data = graph.value.toJSON()
  console.log('预览流程:', data)
  message.info('预览功能开发中')
}

const clearFlow = () => {
  dialog.warning({
    title: '确认清空',
    content: '确定要清空所有节点吗？此操作不可撤销。',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      graph.value.clearCells()
      selectedNode.value = null
      message.success('画布已清空')
    },
  })
}

const zoomIn = () => {
  graph.value.zoom(0.1)
}

const zoomOut = () => {
  graph.value.zoom(-0.1)
}

const zoomToFit = () => {
  graph.value.zoomToFit({ padding: 20 })
}

// 节点属性更新方法
const updateNodeName = () => {
  if (selectedNode.value) {
    selectedNode.value.setLabel(nodeForm.value.name)
    selectedNode.value.setData({
      ...selectedNode.value.getData(),
      name: nodeForm.value.name,
    })
  }
}

const updateNodePosition = () => {
  if (selectedNode.value) {
    selectedNode.value.position(nodeForm.value.left, nodeForm.value.top)
  }
}

// 修复拖拽功能
const startDrag = (nodeType) => {
  // 简化拖拽实现，直接在画布中心创建节点
  const centerX = containerRef.value.offsetWidth / 2
  const centerY = containerRef.value.offsetHeight / 2

  const node = createNodeInCanvas(nodeType, centerX, centerY)
  graph.value.addNode(node)
}

// 在画布中创建节点
const createNodeInCanvas = (type, x, y) => {
  const config = nodeTypes[type]
  const nodeId = `${type}_${Date.now()}`

  return {
    id: nodeId,
    x: x - config.width / 2,
    y: y - config.height / 2,
    ...config,
    label: getNodeLabel(type),
    data: {
      type,
      name: getNodeLabel(type),
    },
    ports: {
      groups: {
        top: {
          position: 'top',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
            },
          },
        },
        right: {
          position: 'right',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
            },
          },
        },
        bottom: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
            },
          },
        },
        left: {
          position: 'left',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
            },
          },
        },
      },
      items: [{ group: 'top' }, { group: 'right' }, { group: 'bottom' }, { group: 'left' }],
    },
  }
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initGraph()
  })
})
</script>

<style scoped>
.flow-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.flow-toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.flow-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.flow-sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  padding: 16px;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 24px;
}

.sidebar-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
  background: white;
}

.node-item:hover {
  border-color: #21438c;
  box-shadow: 0 2px 8px rgba(33, 67, 140, 0.15);
  transform: translateY(-1px);
}

.node-item:active {
  cursor: grabbing;
}

.node-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: white;
  font-size: 14px;
}

.start-node {
  background: #5f95ff;
}

.transform-node {
  background: #ff9c6e;
}

.node-item span {
  font-size: 12px;
  color: #666;
}

.flow-canvas {
  flex: 1;
  position: relative;
  background: #fafafa;
}

.x6-container {
  width: 100%;
  height: 100%;
}

.flow-properties {
  width: 280px;
  background: white;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.properties-header {
  height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  background: #fafafa;
}

.properties-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.properties-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* X6 样式覆盖 */
:deep(.x6-widget-selection-rubberband) {
  border: 1px solid #21438c;
  background-color: rgba(33, 67, 140, 0.1);
}

:deep(.x6-widget-selection-box) {
  opacity: 0;
}

:deep(.x6-node-selected .x6-widget-selection-box) {
  opacity: 1;
  border: 2px solid #21438c;
}

:deep(.x6-port-body) {
  visibility: hidden;
}

:deep(.x6-node:hover .x6-port-body) {
  visibility: visible;
}

:deep(.x6-edge:hover path) {
  stroke: #21438c;
  stroke-width: 3;
}

:deep(.x6-edge-selected path) {
  stroke: #21438c;
  stroke-width: 3;
}
</style>
