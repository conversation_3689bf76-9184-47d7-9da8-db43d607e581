import i18n from '~/i18n'
const { t } = i18n.global

const Layout = () => import('@/layout/index.vue')

export default {
  name: 'Workbench',
  path: '/workbench',
  component: Layout,
  redirect: '/workbench/flow',
  meta: {
    title: '工作台',
    icon: 'icon-park-outline:workbench',
    order: 1,
  },
  children: [
    {
      name: 'FlowEditor',
      path: 'flow',
      component: () => import('./flow/index.vue'),
      meta: {
        title: '流程编辑器',
        icon: 'mdi:flow-chart',
      },
    },
  ],
}
